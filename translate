#!/usr/bin/env python
import argparse

from googletrans import Translator


def main():
    parser = argparse.ArgumentParser(
        description="Python Google Translator as a command-line tool"
    )
    parser.add_argument("text", help="The text you want to translate.")
    parser.add_argument(
        "-d",
        "--dest",
        default="en",
        help="The destination language you want to translate. (Default: en)",
    )
    parser.add_argument(
        "-s",
        "--src",
        default="auto",
        help="The source language you want to translate. (Default: auto)",
    )
    parser.add_argument("-c", "--detect", action="store_true", default=False, help="")
    args = parser.parse_args()
    translator = Translator()

    if args.detect:
        result = translator.detect(args.text)
        result = f"""
[{result.lang}, {result.confidence}] {args.text}
        """.strip()
        print(result)
        return

    result = translator.translate(args.text, dest=args.dest, src=args.src)
    result = f"""
[{result.src}] {result.origin}
    ->
[{result.dest}] {result.text}
[pron.] {result.pronunciation}
    """.strip()
    print(result)


if __name__ == "__main__":
    main()
