Solar theme for Python Sphinx
=============================
Solar is an attempt to create a theme for Sphinx based on the `Solarized <http://ethanschoonover.com/solarized>`_ color scheme.

Preview
-------
http://vimalkumar.in/sphinx-themes/solar

Download
--------
Released versions are available from http://github.com/vkvn/sphinx-themes/downloads

Installation
------------
#. Extract the archive.
#. Modify ``conf.py`` of an existing Sphinx project or create new project using ``sphinx-quickstart``.
#. Change the ``html_theme`` parameter to ``solar``.
#. Change the ``html_theme_path`` to the location containing the extracted archive.

License
-------
`GNU General Public License <http://www.gnu.org/licenses/gpl.html>`_.

Credits
-------
Modified from the default Sphinx theme -- Sphinxdoc

Background pattern from http://subtlepatterns.com.
