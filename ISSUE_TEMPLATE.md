<!-- Before submitting an issue, please consult our docs (https://py-googletrans.readthedocs.io/en/latest/) -->

<!-- Please make sure you are posting an issue pertaining to Googletrans. -->

<!-- Please try not to submit support requests of personal needs. We want to keep the library in a simple way. -->

<!-- ISSUES MISSING IMPORTANT INFORMATION MAY BE CLOSED WITHOUT INVESTIGATION. -->

**Googletrans version:**
- [ ] **4.0.0rc1**
- [ ] **3.1.0a0**
- [ ] **3.0.0**
- [ ] **2.x**

**I'm submitting a ...** 
<!--  (check one with "x") -->
- [ ] bug report
- [ ] feature request

<!-- Please try not to submit support requests of personal needs. We want to keep the library in a simple way. -->

**Current behavior:**
<!-- Describe how the bug manifests. -->

**Expected behavior:**
<!-- Describe what the behavior would be without the bug. -->

**Steps to reproduce:**
<!--  Please explain the steps required to duplicate the issue, especially if you are able to provide a sample application. -->

**Related code:**

<!-- If you are able to illustrate the bug or feature request with an example, please provide a sample application via one of the following means:

-->

```
insert short code snippets here
```

**Other information:**
<!-- List any other information that is relevant to your issue. Stack traces, related issues, suggestions on how to fix, Stack Overflow links, forum links, etc. -->

