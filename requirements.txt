# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
anyio==4.6.2.post1
    # via httpx
certifi==2024.8.30
    # via
    #   httpcore
    #   httpx
h11==0.14.0
    # via httpcore
h2==4.1.0
    # via httpx
hpack==4.0.0
    # via h2
httpcore==1.0.7
    # via httpx
httpx==0.27.2
    # via py-googletrans (pyproject.toml)
hyperframe==6.0.1
    # via h2
idna==3.10
    # via
    #   anyio
    #   httpx
sniffio==1.3.1
    # via
    #   anyio
    #   httpx
