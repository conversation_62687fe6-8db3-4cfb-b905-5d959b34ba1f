[project]
name = "googletrans"
version = "4.0.2"
description = "An unofficial Google Translate API for Python"
readme = "README.rst"
requires-python = ">=3.8"
dependencies = ["httpx[http2]>=0.27.2"]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Intended Audience :: Education",
  "Intended Audience :: End Users/Desktop",
  "License :: OSI Approved :: MIT License",
  "Operating System :: POSIX",
  "Operating System :: Microsoft :: Windows",
  "Operating System :: MacOS :: MacOS X",
  "Topic :: Education",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
]

[project.optional-dependencies]
dev = ["pytest", "pytest-asyncio", "pytest-cov", "ruff>=0.7"]

[tool.setuptools]
license-files = ["LICENSE"]

[tool.uv]
dev-dependencies = ["pytest", "pytest-asyncio", "pytest-cov", "ruff>=0.7"]

[project.scripts]
translate = "googletrans:translate"

[project.urls]
homepage = "https://github.com/ssut/py-googletrans"

[[project.authors]]
name = "Suhun Han"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
